"""
Core addon installation hooks that provide IR population and XML data loading as standard features.

This module provides core hooks that are automatically executed during addon
installation and upgrade to ensure IR model and fields population and XML data
loading happen consistently across all addons.
"""

from typing import Any, Dict

from ...logging import get_logger
from ...utils.schema import <PERSON>hem<PERSON><PERSON>omparator
from ..hooks import HookContext, HookType, post_install_hook

logger = get_logger(__name__)


@post_install_hook(
    addon_name=None, priority=100
)  # Low priority to run after addon-specific hooks
async def core_ir_population_hook(context: HookContext) -> bool:
    """
    Core post-install hook that ensures IR model and fields population
    for any addon installation or upgrade.

    This hook runs after all addon-specific hooks and ensures that:
    1. Database schema is synchronized
    2. IR metadata is populated only after successful schema sync
    3. All models from the installed addon are properly registered

    Args:
        context: Hook context containing addon name, environment, etc.

    Returns:
        True if IR population successful, False otherwise
    """
    if not context.env:
        logger.error("No environment available for core IR population")
        return False

    addon_name = context.addon_name
    logger.info("CORE IR POPULATION: Starting for addon '%s'", addon_name)

    try:
        db_manager = context.env.cr

        # Perform schema sync and IR population with rollback support
        logger.info("Performing schema sync and IR population for addon: %s", addon_name)

        # First perform schema sync
        schema_results = await SchemaComparator.sync_model_tables(addon_name)

        if not schema_results.get("sync_successful", False):
            logger.error(
                "Schema sync failed for addon '%s': %s",
                addon_name, schema_results.get('message')
            )
            return False

        # Then perform IR population with rollback support
        from ...utils.ir import ir_population_manager

        ir_results = await ir_population_manager.populate_ir_metadata_with_rollback(
            db_manager, addon_name, schema_results
        )

        if ir_results.get("status") == "error":
            logger.error(
                "Core IR population failed for addon '%s': %s",
                addon_name, ir_results.get('message')
            )
            if ir_results.get("rollback_performed"):
                logger.info(
                    "IR population changes were rolled back for addon '%s'",
                    addon_name
                )
            return False

        # Log summary
        schema_summary = schema_results.get("summary", schema_results)
        ir_summary = ir_results
        logger.info(
            "✅ Core IR population completed for addon '%s': "
            "%d tables created, %d models populated, %d fields populated",
            addon_name,
            schema_summary.get('tables_created', 0),
            ir_summary.get('models_processed', 0),
            ir_summary.get('fields_processed', 0)
        )

        return True

    except Exception as e:
        logger.error("CORE IR POPULATION: Error for addon '%s': %s", addon_name, e)
        return False


@post_install_hook(
    addon_name=None, priority=150
)  # Lower priority to run after IR population hook (priority 100)
async def core_xml_data_loading_hook(context: HookContext) -> bool:
    """
    Core post-install hook that ensures XML data loading after IR population
    for any addon installation or upgrade.

    This hook runs after IR population and ensures that:
    1. XML data files are loaded only after IR metadata is populated
    2. XML ID validation is performed against populated IR data
    3. Proper error handling and rollback support for data loading

    Args:
        context: Hook context containing addon name, environment, etc.

    Returns:
        True if XML data loading successful, False otherwise
    """
    if not context.env:
        logger.error("No environment available for core XML data loading")
        return False

    addon_name = context.addon_name
    logger.info("CORE XML DATA LOADING: Starting for addon '%s'", addon_name)

    try:
        db_manager = context.env.cr

        # Import XML data loader component
        from ..installers.components.xml_data_loader import XMLDataLoader

        # Create XML data loader instance
        xml_loader = XMLDataLoader()

        # Load XML data files with strict XML ID validation
        # (since IR population is now complete)
        logger.info("Loading XML data files for addon: %s", addon_name)

        xml_results = await xml_loader.load_addon_data_files(
            db_manager, addon_name, strict_xmlid_validation=True
        )

        if not xml_results.get("success", False):
            error_msg = xml_results.get("error", "Unknown XML data loading error")
            logger.error(
                "Core XML data loading failed for addon '%s': %s",
                addon_name, error_msg
            )
            return False

        # Log summary
        files_processed = xml_results.get("files_processed", 0)
        total_loaded = xml_results.get("total_loaded", 0)
        logger.info(
            "✅ Core XML data loading completed for addon '%s': "
            "%d files processed, %d records loaded",
            addon_name, files_processed, total_loaded
        )

        return True

    except Exception as e:
        logger.error("CORE XML DATA LOADING: Error for addon '%s': %s", addon_name, e)
        return False


class AddonIRManager:
    """
    Manager class for handling IR population during addon lifecycle.

    This class provides methods to ensure IR population is properly
    integrated into addon installation and upgrade processes.
    """

    def __init__(self):
        self.logger = get_logger(__name__)

    async def ensure_ir_population_for_addon(
        self, env, addon_name: str
    ) -> Dict[str, Any]:
        """
        Ensure IR population is completed for a specific addon.

        This method can be called manually to ensure an addon's IR metadata
        is properly populated, typically used during addon upgrades or
        when IR data needs to be refreshed.

        Args:
            env: Environment instance
            addon_name: Name of the addon

        Returns:
            Dictionary with operation results
        """
        context = HookContext(
            hook_type=HookType.POST_INSTALL, addon_name=addon_name, env=env, metadata={}
        )

        success = await core_ir_population_hook(context)

        return {
            "success": success,
            "addon_name": addon_name,
            "operation": "ir_population",
        }

    async def validate_ir_population_for_addon(
        self, env, addon_name: str
    ) -> Dict[str, Any]:
        """
        Validate that IR population is complete for an addon.

        This method checks if all models and fields for an addon
        are properly registered in the IR tables.

        Args:
            env: Environment instance
            addon_name: Name of the addon

        Returns:
            Dictionary with validation results
        """
        try:
            # Get all models for the addon
            from ...utils.ir import ir_population_manager

            validation_results = (
                await ir_population_manager.validate_ir_metadata_for_addon(
                    env.cr, addon_name
                )
            )

            return {
                "success": True,
                "addon_name": addon_name,
                "validation_results": validation_results,
            }

        except Exception as e:
            self.logger.error(f"IR validation failed for addon '{addon_name}': {e}")
            return {"success": False, "addon_name": addon_name, "error": str(e)}


# Global instance for easy access
addon_ir_manager = AddonIRManager()
